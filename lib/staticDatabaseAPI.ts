/**
 * Static Database API for Disc Golf Inventory Management System
 *
 * This module provides access to curated static disc golf databases including
 * PDGA approved discs, community flight numbers, and manufacturer-specific collections.
 */

import type { Disc } from "./types";
import type { APIServiceResult } from "./discDatabaseAPI";
import { generateDiscId } from "./discUtils";
import { DiscCondition, Location } from "./types";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Static database provider types
 */
export type StaticDatabaseProvider = "pdga" | "community" | "manufacturer";

/**
 * PDGA approved disc format
 */
export interface PDGADisc {
  manufacturer: string;
  model: string;
  approvedDate: string;
  class: "Super Class" | "Vintage Class" | "Super Class & Vintage Class";
}

/**
 * Community flight numbers disc format
 */
export interface CommunityDisc {
  manufacturer: string;
  mold: string;
  speed: number;
  glide: number;
  turn: number;
  fade: number;
  stability: string;
  category: string;
  plasticTypes?: string[];
  averageWeight?: number;
  communityRating?: number;
  reviewCount?: number;
}

/**
 * Manufacturer-specific disc format
 */
export interface ManufacturerDisc {
  manufacturer: string;
  mold: string;
  speed: number;
  glide: number;
  turn: number;
  fade: number;
  category: string;
  plasticTypes: string[];
  weightRange: {
    min: number;
    max: number;
  };
  stability: string;
  description?: string;
  discontinued?: boolean;
  releaseYear?: number;
}

/**
 * Static database search filters
 */
export interface StaticDatabaseFilters {
  provider?: StaticDatabaseProvider;
  manufacturer?: string;
  category?: string;
  speed?: number;
  stability?: string;
  searchQuery?: string;
  limit?: number;
  includeDiscontinued?: boolean;
}

/**
 * Static database metadata
 */
export interface StaticDatabaseMetadata {
  provider: StaticDatabaseProvider;
  name: string;
  description: string;
  lastUpdated: string;
  totalDiscs: number;
  version: string;
}

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * Static database configuration
 */
export const STATIC_DATABASE_CONFIG = {
  cacheTimeout: 1000 * 60 * 60, // 1 hour
  maxCacheSize: 10000, // Maximum number of cached discs
  defaultLimit: 50,
  maxLimit: 200,
} as const;

/**
 * Available static databases
 */
export const STATIC_DATABASES: Record<StaticDatabaseProvider, StaticDatabaseMetadata> = {
  pdga: {
    provider: "pdga",
    name: "PDGA Approved Discs",
    description: "Official PDGA approved disc golf discs for tournament play",
    lastUpdated: "2025-01-16",
    totalDiscs: 2500,
    version: "1.0.0",
  },
  community: {
    provider: "community",
    name: "Community Flight Numbers",
    description: "Community-curated flight numbers and disc reviews",
    lastUpdated: "2025-01-16",
    totalDiscs: 1800,
    version: "1.0.0",
  },
  manufacturer: {
    provider: "manufacturer",
    name: "Manufacturer Collections",
    description: "Comprehensive manufacturer-specific disc collections (7 manufacturers)",
    lastUpdated: "2025-01-16",
    totalDiscs: 78,
    version: "2.0.0",
  },
} as const;

// ============================================================================
// CACHE MANAGEMENT
// ============================================================================

/**
 * In-memory cache for static database data
 */
class StaticDatabaseCache {
  private cache = new Map<string, { data: any; timestamp: number }>();

  set(key: string, data: any): void {
    // Clear old entries if cache is getting too large
    if (this.cache.size >= STATIC_DATABASE_CONFIG.maxCacheSize) {
      this.clearOldEntries();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check if entry has expired
    if (Date.now() - entry.timestamp > STATIC_DATABASE_CONFIG.cacheTimeout) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  clear(): void {
    this.cache.clear();
  }

  private clearOldEntries(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > STATIC_DATABASE_CONFIG.cacheTimeout) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach((key) => this.cache.delete(key));

    // If still too large, remove oldest entries
    if (this.cache.size >= STATIC_DATABASE_CONFIG.maxCacheSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

      const toRemove = entries.slice(0, Math.floor(this.cache.size * 0.2));
      toRemove.forEach(([key]) => this.cache.delete(key));
    }
  }
}

// Global cache instance
const staticDatabaseCache = new StaticDatabaseCache();

// ============================================================================
// DATA LOADING UTILITIES
// ============================================================================

/**
 * Load static database data from JSON files
 */
async function loadStaticDatabaseData(provider: StaticDatabaseProvider): Promise<any[]> {
  const cacheKey = `static_db_${provider}`;

  // Check cache first
  const cached = staticDatabaseCache.get(cacheKey);
  if (cached) {
    return cached;
  }

  try {
    // Load data from JSON files
    const data = await loadDataFromFiles(provider);

    // Cache the data
    staticDatabaseCache.set(cacheKey, data);

    return data;
  } catch (error) {
    console.error(`Failed to load static database data for ${provider}:`, error);
    // Fallback to sample data for development
    return await loadSampleData(provider);
  }
}

/**
 * Load data from actual JSON files
 */
async function loadDataFromFiles(provider: StaticDatabaseProvider): Promise<any[]> {
  let dataPath: string;

  switch (provider) {
    case "pdga":
      dataPath = "/data/pdga/approved-discs.json";
      break;
    case "community":
      dataPath = "/data/community/flight-numbers.json";
      break;
    case "manufacturer":
      // For manufacturer data, we'll combine multiple files
      return await loadManufacturerData();
    default:
      throw new Error(`Unknown provider: ${provider}`);
  }

  const response = await fetch(dataPath);
  if (!response.ok) {
    throw new Error(`Failed to load data from ${dataPath}: ${response.statusText}`);
  }

  return await response.json();
}

/**
 * Load and combine manufacturer data from multiple files
 */
async function loadManufacturerData(): Promise<ManufacturerDisc[]> {
  const manufacturers = ["innova", "discraft", "dynamic-discs", "latitude-64", "mvp", "axiom", "streamline"]; // Available manufacturer files
  const allData: ManufacturerDisc[] = [];

  for (const manufacturer of manufacturers) {
    try {
      const response = await fetch(`/data/manufacturers/${manufacturer}.json`);
      if (response.ok) {
        const data = await response.json();
        allData.push(...data);
      }
    } catch (error) {
      console.warn(`Failed to load data for manufacturer ${manufacturer}:`, error);
    }
  }

  return allData;
}

/**
 * Load sample data for development/testing
 * In production, this would be replaced with actual data loading
 */
async function loadSampleData(provider: StaticDatabaseProvider): Promise<any[]> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 100));

  switch (provider) {
    case "pdga":
      return generateSamplePDGAData();
    case "community":
      return generateSampleCommunityData();
    case "manufacturer":
      return generateSampleManufacturerData();
    default:
      return [];
  }
}

/**
 * Generate sample PDGA data
 */
function generateSamplePDGAData(): PDGADisc[] {
  return [
    {
      manufacturer: "Innova Champion Discs",
      model: "Destroyer",
      approvedDate: "2007-03-15",
      class: "Super Class",
    },
    {
      manufacturer: "Discraft",
      model: "Buzzz",
      approvedDate: "2003-08-20",
      class: "Super Class",
    },
    {
      manufacturer: "Dynamic Discs",
      model: "Judge",
      approvedDate: "2010-05-12",
      class: "Super Class",
    },
    {
      manufacturer: "Latitude 64",
      model: "River",
      approvedDate: "2009-11-08",
      class: "Super Class",
    },
    {
      manufacturer: "MVP Disc Sports",
      model: "Neutron",
      approvedDate: "2012-02-14",
      class: "Super Class",
    },
  ];
}

/**
 * Generate sample community data
 */
function generateSampleCommunityData(): CommunityDisc[] {
  return [
    {
      manufacturer: "Innova",
      mold: "Destroyer",
      speed: 12,
      glide: 5,
      turn: -1,
      fade: 3,
      stability: "Overstable",
      category: "Distance Driver",
      plasticTypes: ["Champion", "Star", "DX"],
      averageWeight: 175,
      communityRating: 4.5,
      reviewCount: 1250,
    },
    {
      manufacturer: "Discraft",
      mold: "Buzzz",
      speed: 5,
      glide: 4,
      turn: -1,
      fade: 1,
      stability: "Stable",
      category: "Midrange",
      plasticTypes: ["ESP", "Z", "Pro-D"],
      averageWeight: 177,
      communityRating: 4.8,
      reviewCount: 2100,
    },
  ];
}

/**
 * Generate sample manufacturer data
 */
function generateSampleManufacturerData(): ManufacturerDisc[] {
  return [
    {
      manufacturer: "Innova",
      mold: "Destroyer",
      speed: 12,
      glide: 5,
      turn: -1,
      fade: 3,
      category: "Distance Driver",
      plasticTypes: ["Champion", "Star", "DX", "Pro", "GStar"],
      weightRange: { min: 165, max: 175 },
      stability: "Overstable",
      description: "The Destroyer is a fast, stable power driver with significant glide.",
      discontinued: false,
      releaseYear: 2007,
    },
    {
      manufacturer: "Latitude 64",
      mold: "River",
      speed: 7,
      glide: 7,
      turn: -1,
      fade: 1,
      category: "Fairway Driver",
      plasticTypes: ["Opto", "Gold", "Retro"],
      weightRange: { min: 165, max: 175 },
      stability: "Understable",
      description: "The River is an understable fairway driver with excellent glide.",
      discontinued: false,
      releaseYear: 2009,
    },
    {
      manufacturer: "MVP Disc Sports",
      mold: "Neutron",
      speed: 3,
      glide: 3,
      turn: 0,
      fade: 2,
      category: "Putter",
      plasticTypes: ["Neutron", "Electron"],
      weightRange: { min: 170, max: 176 },
      stability: "Stable",
      description: "The Neutron is MVP's flagship putter with gyroscopic technology.",
      discontinued: false,
      releaseYear: 2012,
    },
    {
      manufacturer: "Axiom Discs",
      mold: "Envy",
      speed: 3,
      glide: 3,
      turn: 0,
      fade: 2,
      category: "Putter",
      plasticTypes: ["Neutron", "Electron", "Proton"],
      weightRange: { min: 170, max: 176 },
      stability: "Stable",
      description: "The Envy is a slightly overstable putter with reliable fade.",
      discontinued: false,
      releaseYear: 2014,
    },
  ];
}

// ============================================================================
// SEARCH FUNCTIONS
// ============================================================================

/**
 * Search static databases with filters
 */
export async function searchStaticDatabase(filters: StaticDatabaseFilters = {}): Promise<APIServiceResult<Disc[]>> {
  try {
    const { provider = "community", limit = STATIC_DATABASE_CONFIG.defaultLimit } = filters;

    // Load data for the specified provider
    const rawData = await loadStaticDatabaseData(provider);

    // Filter the data
    const filteredData = filterStaticData(rawData, filters);

    // Apply limit
    const limitedData = filteredData.slice(0, Math.min(limit, STATIC_DATABASE_CONFIG.maxLimit));

    // Transform to Disc format
    const transformedData = transformStaticDataToDiscs(limitedData, provider);

    return {
      success: true,
      data: transformedData,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to search static database",
    };
  }
}

/**
 * Filter static database data based on search criteria
 */
function filterStaticData(data: any[], filters: StaticDatabaseFilters): any[] {
  let filtered = [...data];

  // Filter by manufacturer
  if (filters.manufacturer) {
    const manufacturerLower = filters.manufacturer.toLowerCase();
    filtered = filtered.filter((disc) => disc.manufacturer?.toLowerCase().includes(manufacturerLower));
  }

  // Filter by category
  if (filters.category) {
    const categoryLower = filters.category.toLowerCase();
    filtered = filtered.filter((disc) => disc.category?.toLowerCase().includes(categoryLower));
  }

  // Filter by speed
  if (filters.speed !== undefined) {
    filtered = filtered.filter((disc) => disc.speed === filters.speed);
  }

  // Filter by stability
  if (filters.stability) {
    const stabilityLower = filters.stability.toLowerCase();
    filtered = filtered.filter((disc) => disc.stability?.toLowerCase().includes(stabilityLower));
  }

  // Filter by search query (searches manufacturer and mold/model)
  if (filters.searchQuery) {
    const queryLower = filters.searchQuery.toLowerCase();
    filtered = filtered.filter((disc) => {
      const manufacturer = disc.manufacturer?.toLowerCase() || "";
      const mold = disc.mold?.toLowerCase() || disc.model?.toLowerCase() || "";
      return manufacturer.includes(queryLower) || mold.includes(queryLower);
    });
  }

  // Filter discontinued discs (for manufacturer data)
  if (filters.includeDiscontinued === false) {
    filtered = filtered.filter((disc) => disc.discontinued !== true);
  }

  return filtered;
}

/**
 * Transform static database data to Disc format
 */
function transformStaticDataToDiscs(data: any[], provider: StaticDatabaseProvider): Disc[] {
  const now = new Date();

  return data.map((item) => {
    const baseDisc: Disc = {
      id: generateDiscId(),
      manufacturer: item.manufacturer || "Unknown",
      mold: item.mold || item.model || "Unknown",
      plasticType: getDefaultPlasticType(item, provider),
      weight: getDefaultWeight(item, provider),
      condition: DiscCondition.NEW,
      flightNumbers: getFlightNumbers(item, provider),
      color: "Unknown",
      notes: generateNotes(item, provider),
      currentLocation: Location.HOME,
      imageUrl: undefined,
      createdAt: now,
      updatedAt: now,
    };

    return baseDisc;
  });
}

/**
 * Get default plastic type based on provider and data
 */
function getDefaultPlasticType(item: any, provider: StaticDatabaseProvider): string {
  switch (provider) {
    case "manufacturer":
    case "community":
      return item.plasticTypes?.[0] || "Unknown";
    case "pdga":
      return "Unknown"; // PDGA data doesn't include plastic types
    default:
      return "Unknown";
  }
}

/**
 * Get default weight based on provider and data
 */
function getDefaultWeight(item: any, provider: StaticDatabaseProvider): number {
  switch (provider) {
    case "community":
      return item.averageWeight || 175;
    case "manufacturer":
      return item.weightRange?.max || 175;
    case "pdga":
      return 175; // Default weight
    default:
      return 175;
  }
}

/**
 * Get flight numbers based on provider and data
 */
function getFlightNumbers(
  item: any,
  provider: StaticDatabaseProvider
): { speed: number; glide: number; turn: number; fade: number } {
  switch (provider) {
    case "community":
    case "manufacturer":
      return {
        speed: item.speed || 5,
        glide: item.glide || 4,
        turn: item.turn || 0,
        fade: item.fade || 1,
      };
    case "pdga":
      // PDGA data doesn't include flight numbers, use defaults
      return {
        speed: 5,
        glide: 4,
        turn: 0,
        fade: 1,
      };
    default:
      return {
        speed: 5,
        glide: 4,
        turn: 0,
        fade: 1,
      };
  }
}

/**
 * Generate notes based on provider and data
 */
function generateNotes(item: any, provider: StaticDatabaseProvider): string {
  const notes: string[] = [];

  switch (provider) {
    case "pdga":
      notes.push(`Imported from PDGA Approved Discs database`);
      if (item.approvedDate) {
        notes.push(`PDGA Approved: ${item.approvedDate}`);
      }
      if (item.class) {
        notes.push(`Class: ${item.class}`);
      }
      break;

    case "community":
      notes.push(`Imported from Community Flight Numbers database`);
      if (item.communityRating) {
        notes.push(`Community Rating: ${item.communityRating}/5`);
      }
      if (item.reviewCount) {
        notes.push(`Reviews: ${item.reviewCount}`);
      }
      break;

    case "manufacturer":
      notes.push(`Imported from Manufacturer database`);
      if (item.description) {
        notes.push(item.description);
      }
      if (item.releaseYear) {
        notes.push(`Released: ${item.releaseYear}`);
      }
      if (item.discontinued) {
        notes.push(`Status: Discontinued`);
      }
      break;
  }

  return notes.join(". ");
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get available static database providers
 */
export function getStaticDatabaseProviders(): StaticDatabaseMetadata[] {
  return Object.values(STATIC_DATABASES);
}

/**
 * Get metadata for a specific provider
 */
export function getStaticDatabaseMetadata(provider: StaticDatabaseProvider): StaticDatabaseMetadata | null {
  return STATIC_DATABASES[provider] || null;
}

/**
 * Clear static database cache
 */
export function clearStaticDatabaseCache(): void {
  staticDatabaseCache.clear();
}

/**
 * Get available manufacturers from static databases
 */
export async function getStaticDatabaseManufacturers(
  provider: StaticDatabaseProvider
): Promise<APIServiceResult<string[]>> {
  try {
    const data = await loadStaticDatabaseData(provider);
    const manufacturers = [...new Set(data.map((item) => item.manufacturer))].filter(Boolean).sort();

    return {
      success: true,
      data: manufacturers,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get manufacturers",
    };
  }
}

/**
 * Get available categories from static databases
 */
export async function getStaticDatabaseCategories(
  provider: StaticDatabaseProvider
): Promise<APIServiceResult<string[]>> {
  try {
    const data = await loadStaticDatabaseData(provider);
    const categories = [...new Set(data.map((item) => item.category))].filter(Boolean).sort();

    return {
      success: true,
      data: categories,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get categories",
    };
  }
}

// ============================================================================
// PDGA-SPECIFIC FUNCTIONS
// ============================================================================

/**
 * Search PDGA approved discs with PDGA-specific filters
 */
export async function searchPDGAApprovedDiscs(
  filters: {
    manufacturer?: string;
    searchQuery?: string;
    class?: "Super Class" | "Vintage Class" | "Super Class & Vintage Class";
    approvedAfter?: string; // Date string
    approvedBefore?: string; // Date string
    limit?: number;
  } = {}
): Promise<APIServiceResult<Disc[]>> {
  try {
    const data = await loadStaticDatabaseData("pdga");
    let filtered = [...data];

    // Filter by manufacturer
    if (filters.manufacturer) {
      const manufacturerLower = filters.manufacturer.toLowerCase();
      filtered = filtered.filter((disc) => disc.manufacturer?.toLowerCase().includes(manufacturerLower));
    }

    // Filter by search query
    if (filters.searchQuery) {
      const queryLower = filters.searchQuery.toLowerCase();
      filtered = filtered.filter((disc) => {
        const manufacturer = disc.manufacturer?.toLowerCase() || "";
        const model = disc.model?.toLowerCase() || "";
        return manufacturer.includes(queryLower) || model.includes(queryLower);
      });
    }

    // Filter by class
    if (filters.class) {
      filtered = filtered.filter((disc) => disc.class === filters.class);
    }

    // Filter by approval date range
    if (filters.approvedAfter) {
      const afterDate = new Date(filters.approvedAfter);
      filtered = filtered.filter((disc) => {
        const approvedDate = new Date(disc.approvedDate);
        return approvedDate >= afterDate;
      });
    }

    if (filters.approvedBefore) {
      const beforeDate = new Date(filters.approvedBefore);
      filtered = filtered.filter((disc) => {
        const approvedDate = new Date(disc.approvedDate);
        return approvedDate <= beforeDate;
      });
    }

    // Apply limit
    const limit = Math.min(filters.limit || 50, 200);
    const limitedData = filtered.slice(0, limit);

    // Transform to Disc format
    const transformedData = transformStaticDataToDiscs(limitedData, "pdga");

    return {
      success: true,
      data: transformedData,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to search PDGA approved discs",
    };
  }
}

/**
 * Get PDGA approval classes
 */
export function getPDGAApprovalClasses(): string[] {
  return ["Super Class", "Vintage Class", "Super Class & Vintage Class"];
}

/**
 * Get PDGA statistics
 */
export async function getPDGAStatistics(): Promise<
  APIServiceResult<{
    totalDiscs: number;
    manufacturerCount: number;
    classBreakdown: Record<string, number>;
    recentApprovals: number; // Last 30 days
    oldestApproval: string;
    newestApproval: string;
  }>
> {
  try {
    const data = await loadStaticDatabaseData("pdga");

    const manufacturers = new Set(data.map((disc) => disc.manufacturer));
    const classBreakdown: Record<string, number> = {};

    // Calculate class breakdown
    data.forEach((disc) => {
      classBreakdown[disc.class] = (classBreakdown[disc.class] || 0) + 1;
    });

    // Find date range
    const dates = data.map((disc) => new Date(disc.approvedDate)).sort((a, b) => a.getTime() - b.getTime());
    const oldestApproval = dates[0]?.toISOString().split("T")[0] || "";
    const newestApproval = dates[dates.length - 1]?.toISOString().split("T")[0] || "";

    // Count recent approvals (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentApprovals = data.filter((disc) => new Date(disc.approvedDate) >= thirtyDaysAgo).length;

    return {
      success: true,
      data: {
        totalDiscs: data.length,
        manufacturerCount: manufacturers.size,
        classBreakdown,
        recentApprovals,
        oldestApproval,
        newestApproval,
      },
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get PDGA statistics",
    };
  }
}

// ============================================================================
// COMMUNITY DATABASE-SPECIFIC FUNCTIONS
// ============================================================================

/**
 * Search community flight numbers database with community-specific filters
 */
export async function searchCommunityFlightNumbers(
  filters: {
    manufacturer?: string;
    category?: string;
    speed?: number;
    stability?: string;
    searchQuery?: string;
    minRating?: number;
    minReviews?: number;
    sortBy?: "rating" | "reviews" | "name" | "speed";
    sortOrder?: "asc" | "desc";
    limit?: number;
  } = {}
): Promise<APIServiceResult<Disc[]>> {
  try {
    const data = await loadStaticDatabaseData("community");
    let filtered = [...data];

    // Apply filters
    if (filters.manufacturer) {
      const manufacturerLower = filters.manufacturer.toLowerCase();
      filtered = filtered.filter((disc) => disc.manufacturer?.toLowerCase().includes(manufacturerLower));
    }

    if (filters.category) {
      const categoryLower = filters.category.toLowerCase();
      filtered = filtered.filter((disc) => disc.category?.toLowerCase().includes(categoryLower));
    }

    if (filters.speed !== undefined) {
      filtered = filtered.filter((disc) => disc.speed === filters.speed);
    }

    if (filters.stability) {
      const stabilityLower = filters.stability.toLowerCase();
      filtered = filtered.filter((disc) => disc.stability?.toLowerCase().includes(stabilityLower));
    }

    if (filters.searchQuery) {
      const queryLower = filters.searchQuery.toLowerCase();
      filtered = filtered.filter((disc) => {
        const manufacturer = disc.manufacturer?.toLowerCase() || "";
        const mold = disc.mold?.toLowerCase() || "";
        return manufacturer.includes(queryLower) || mold.includes(queryLower);
      });
    }

    if (filters.minRating !== undefined) {
      filtered = filtered.filter((disc) => (disc.communityRating || 0) >= filters.minRating!);
    }

    if (filters.minReviews !== undefined) {
      filtered = filtered.filter((disc) => (disc.reviewCount || 0) >= filters.minReviews!);
    }

    // Apply sorting
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (filters.sortBy) {
          case "rating":
            aValue = a.communityRating || 0;
            bValue = b.communityRating || 0;
            break;
          case "reviews":
            aValue = a.reviewCount || 0;
            bValue = b.reviewCount || 0;
            break;
          case "name":
            aValue = a.mold || "";
            bValue = b.mold || "";
            break;
          case "speed":
            aValue = a.speed || 0;
            bValue = b.speed || 0;
            break;
          default:
            return 0;
        }

        if (filters.sortOrder === "desc") {
          return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
        } else {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
      });
    }

    // Apply limit
    const limit = Math.min(filters.limit || 50, 200);
    const limitedData = filtered.slice(0, limit);

    // Transform to Disc format
    const transformedData = transformStaticDataToDiscs(limitedData, "community");

    return {
      success: true,
      data: transformedData,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to search community flight numbers",
    };
  }
}

/**
 * Get top-rated discs from community database
 */
export async function getTopRatedDiscs(
  options: {
    category?: string;
    minReviews?: number;
    limit?: number;
  } = {}
): Promise<APIServiceResult<Disc[]>> {
  const filters = {
    ...options,
    minRating: 4.0,
    minReviews: options.minReviews || 100,
    sortBy: "rating" as const,
    sortOrder: "desc" as const,
    limit: options.limit || 20,
  };

  return searchCommunityFlightNumbers(filters);
}

/**
 * Get community database statistics
 */
export async function getCommunityStatistics(): Promise<
  APIServiceResult<{
    totalDiscs: number;
    manufacturerCount: number;
    categoryBreakdown: Record<string, number>;
    averageRating: number;
    totalReviews: number;
    topRatedDisc: { mold: string; manufacturer: string; rating: number } | null;
    mostReviewedDisc: { mold: string; manufacturer: string; reviews: number } | null;
  }>
> {
  try {
    const data = await loadStaticDatabaseData("community");

    const manufacturers = new Set(data.map((disc) => disc.manufacturer));
    const categoryBreakdown: Record<string, number> = {};

    // Calculate category breakdown
    data.forEach((disc) => {
      categoryBreakdown[disc.category] = (categoryBreakdown[disc.category] || 0) + 1;
    });

    // Calculate averages
    const totalRating = data.reduce((sum, disc) => sum + (disc.communityRating || 0), 0);
    const totalReviews = data.reduce((sum, disc) => sum + (disc.reviewCount || 0), 0);
    const averageRating = data.length > 0 ? totalRating / data.length : 0;

    // Find top rated and most reviewed
    const topRatedDisc = data.reduce((top, disc) => {
      if (!top || (disc.communityRating || 0) > (top.communityRating || 0)) {
        return disc;
      }
      return top;
    }, null as any);

    const mostReviewedDisc = data.reduce((top, disc) => {
      if (!top || (disc.reviewCount || 0) > (top.reviewCount || 0)) {
        return disc;
      }
      return top;
    }, null as any);

    return {
      success: true,
      data: {
        totalDiscs: data.length,
        manufacturerCount: manufacturers.size,
        categoryBreakdown,
        averageRating: Math.round(averageRating * 10) / 10,
        totalReviews,
        topRatedDisc: topRatedDisc
          ? {
              mold: topRatedDisc.mold,
              manufacturer: topRatedDisc.manufacturer,
              rating: topRatedDisc.communityRating,
            }
          : null,
        mostReviewedDisc: mostReviewedDisc
          ? {
              mold: mostReviewedDisc.mold,
              manufacturer: mostReviewedDisc.manufacturer,
              reviews: mostReviewedDisc.reviewCount,
            }
          : null,
      },
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get community statistics",
    };
  }
}

// ============================================================================
// MANUFACTURER DATABASE-SPECIFIC FUNCTIONS
// ============================================================================

/**
 * Search manufacturer database with manufacturer-specific filters
 */
export async function searchManufacturerDatabase(
  filters: {
    manufacturer?: string;
    category?: string;
    speed?: number;
    stability?: string;
    searchQuery?: string;
    plasticType?: string;
    includeDiscontinued?: boolean;
    releaseYear?: number;
    releaseYearRange?: { min?: number; max?: number };
    sortBy?: "name" | "speed" | "releaseYear" | "manufacturer";
    sortOrder?: "asc" | "desc";
    limit?: number;
  } = {}
): Promise<APIServiceResult<Disc[]>> {
  try {
    const data = await loadStaticDatabaseData("manufacturer");
    let filtered = [...data];

    // Apply filters
    if (filters.manufacturer) {
      const manufacturerLower = filters.manufacturer.toLowerCase();
      filtered = filtered.filter((disc) => disc.manufacturer?.toLowerCase().includes(manufacturerLower));
    }

    if (filters.category) {
      const categoryLower = filters.category.toLowerCase();
      filtered = filtered.filter((disc) => disc.category?.toLowerCase().includes(categoryLower));
    }

    if (filters.speed !== undefined) {
      filtered = filtered.filter((disc) => disc.speed === filters.speed);
    }

    if (filters.stability) {
      const stabilityLower = filters.stability.toLowerCase();
      filtered = filtered.filter((disc) => disc.stability?.toLowerCase().includes(stabilityLower));
    }

    if (filters.searchQuery) {
      const queryLower = filters.searchQuery.toLowerCase();
      filtered = filtered.filter((disc) => {
        const manufacturer = disc.manufacturer?.toLowerCase() || "";
        const mold = disc.mold?.toLowerCase() || "";
        const description = disc.description?.toLowerCase() || "";
        return manufacturer.includes(queryLower) || mold.includes(queryLower) || description.includes(queryLower);
      });
    }

    if (filters.plasticType) {
      const plasticLower = filters.plasticType.toLowerCase();
      filtered = filtered.filter((disc) =>
        disc.plasticTypes?.some((plastic) => plastic.toLowerCase().includes(plasticLower))
      );
    }

    if (filters.includeDiscontinued === false) {
      filtered = filtered.filter((disc) => disc.discontinued !== true);
    }

    if (filters.releaseYear !== undefined) {
      filtered = filtered.filter((disc) => disc.releaseYear === filters.releaseYear);
    }

    if (filters.releaseYearRange) {
      if (filters.releaseYearRange.min !== undefined) {
        filtered = filtered.filter((disc) => (disc.releaseYear || 0) >= filters.releaseYearRange!.min!);
      }
      if (filters.releaseYearRange.max !== undefined) {
        filtered = filtered.filter((disc) => (disc.releaseYear || 0) <= filters.releaseYearRange!.max!);
      }
    }

    // Apply sorting
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (filters.sortBy) {
          case "name":
            aValue = a.mold || "";
            bValue = b.mold || "";
            break;
          case "speed":
            aValue = a.speed || 0;
            bValue = b.speed || 0;
            break;
          case "releaseYear":
            aValue = a.releaseYear || 0;
            bValue = b.releaseYear || 0;
            break;
          case "manufacturer":
            aValue = a.manufacturer || "";
            bValue = b.manufacturer || "";
            break;
          default:
            return 0;
        }

        if (filters.sortOrder === "desc") {
          return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
        } else {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
      });
    }

    // Apply limit
    const limit = Math.min(filters.limit || 50, 200);
    const limitedData = filtered.slice(0, limit);

    // Transform to Disc format
    const transformedData = transformStaticDataToDiscs(limitedData, "manufacturer");

    return {
      success: true,
      data: transformedData,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to search manufacturer database",
    };
  }
}

/**
 * Get discs by specific manufacturer
 */
export async function getDiscsByManufacturer(
  manufacturer: string,
  options: {
    includeDiscontinued?: boolean;
    category?: string;
    limit?: number;
  } = {}
): Promise<APIServiceResult<Disc[]>> {
  const filters = {
    manufacturer,
    includeDiscontinued: options.includeDiscontinued ?? true,
    category: options.category,
    sortBy: "name" as const,
    sortOrder: "asc" as const,
    limit: options.limit || 100,
  };

  return searchManufacturerDatabase(filters);
}

/**
 * Get available plastic types for a manufacturer
 */
export async function getManufacturerPlasticTypes(manufacturer?: string): Promise<APIServiceResult<string[]>> {
  try {
    const data = await loadStaticDatabaseData("manufacturer");
    let filtered = data;

    if (manufacturer) {
      const manufacturerLower = manufacturer.toLowerCase();
      filtered = data.filter((disc) => disc.manufacturer?.toLowerCase().includes(manufacturerLower));
    }

    const plasticTypes = new Set<string>();
    filtered.forEach((disc) => {
      disc.plasticTypes?.forEach((plastic) => plasticTypes.add(plastic));
    });

    return {
      success: true,
      data: Array.from(plasticTypes).sort(),
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get plastic types",
    };
  }
}

/**
 * Get manufacturer database statistics
 */
export async function getManufacturerStatistics(): Promise<
  APIServiceResult<{
    totalDiscs: number;
    manufacturerCount: number;
    categoryBreakdown: Record<string, number>;
    plasticTypeCount: number;
    discontinuedCount: number;
    averageSpeed: number;
    oldestDisc: { mold: string; manufacturer: string; year: number } | null;
    newestDisc: { mold: string; manufacturer: string; year: number } | null;
    manufacturerBreakdown: Record<string, number>;
  }>
> {
  try {
    const data = await loadStaticDatabaseData("manufacturer");

    const manufacturers = new Set(data.map((disc) => disc.manufacturer));
    const categoryBreakdown: Record<string, number> = {};
    const manufacturerBreakdown: Record<string, number> = {};
    const plasticTypes = new Set<string>();

    // Calculate breakdowns
    data.forEach((disc) => {
      categoryBreakdown[disc.category] = (categoryBreakdown[disc.category] || 0) + 1;
      manufacturerBreakdown[disc.manufacturer] = (manufacturerBreakdown[disc.manufacturer] || 0) + 1;
      disc.plasticTypes?.forEach((plastic) => plasticTypes.add(plastic));
    });

    // Calculate averages and counts
    const totalSpeed = data.reduce((sum, disc) => sum + (disc.speed || 0), 0);
    const averageSpeed = data.length > 0 ? totalSpeed / data.length : 0;
    const discontinuedCount = data.filter((disc) => disc.discontinued === true).length;

    // Find oldest and newest discs
    const discsWithYear = data.filter((disc) => disc.releaseYear);
    const oldestDisc = discsWithYear.reduce((oldest, disc) => {
      if (!oldest || (disc.releaseYear || 0) < (oldest.releaseYear || 0)) {
        return disc;
      }
      return oldest;
    }, null as any);

    const newestDisc = discsWithYear.reduce((newest, disc) => {
      if (!newest || (disc.releaseYear || 0) > (newest.releaseYear || 0)) {
        return disc;
      }
      return newest;
    }, null as any);

    return {
      success: true,
      data: {
        totalDiscs: data.length,
        manufacturerCount: manufacturers.size,
        categoryBreakdown,
        plasticTypeCount: plasticTypes.size,
        discontinuedCount,
        averageSpeed: Math.round(averageSpeed * 10) / 10,
        oldestDisc: oldestDisc
          ? {
              mold: oldestDisc.mold,
              manufacturer: oldestDisc.manufacturer,
              year: oldestDisc.releaseYear,
            }
          : null,
        newestDisc: newestDisc
          ? {
              mold: newestDisc.mold,
              manufacturer: newestDisc.manufacturer,
              year: newestDisc.releaseYear,
            }
          : null,
        manufacturerBreakdown,
      },
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get manufacturer statistics",
    };
  }
}
