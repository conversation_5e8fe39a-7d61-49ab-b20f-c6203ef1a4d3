/**
 * Integration Test for Expanded Manufacturer Collections
 *
 * Tests the new manufacturer collections (Latitude 64, MVP, Axiom, Streamline)
 * and validates their integration with the static database system.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import {
  searchManufacturerDatabase,
  getDiscsByManufacturer,
  getManufacturerPlasticTypes,
  getManufacturerStatistics,
  clearStaticDatabaseCache,
} from "@/lib/staticDatabaseAPI";

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe("Expanded Manufacturer Collections", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    clearStaticDatabaseCache();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("Latitude 64 Collection", () => {
    const mockLatitude64Data = [
      {
        manufacturer: "Latitude 64",
        mold: "River",
        speed: 7,
        glide: 7,
        turn: -1,
        fade: 1,
        category: "Fairway Driver",
        plasticTypes: ["Opto", "Gold", "Retro"],
        weightRange: { min: 165, max: 175 },
        stability: "Understable",
        discontinued: false,
        releaseYear: 2009,
      },
      {
        manufacturer: "Latitude 64",
        mold: "Pure",
        speed: 3,
        glide: 3,
        turn: -1,
        fade: 1,
        category: "Putter",
        plasticTypes: ["Opto", "Zero Soft", "Zero Medium"],
        weightRange: { min: 170, max: 176 },
        stability: "Stable",
        discontinued: false,
        releaseYear: 2008,
      },
    ];

    it("should search Latitude 64 discs successfully", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockLatitude64Data),
      });

      const result = await searchManufacturerDatabase({
        manufacturer: "Latitude 64",
        includeDiscontinued: false,
      });

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data![0].manufacturer).toBe("Latitude 64");
      expect(result.data![0].mold).toBe("River");
      expect(result.data![1].mold).toBe("Pure");
    });

    it("should filter by Latitude 64 plastic types", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockLatitude64Data),
      });

      const result = await searchManufacturerDatabase({
        manufacturer: "Latitude 64",
        plasticType: "Opto",
      });

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2); // Both have Opto plastic
      expect(result.data!.every(disc => disc.plasticType === "Opto")).toBe(true);
    });

    it("should get Latitude 64 plastic types", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockLatitude64Data),
      });

      const result = await getManufacturerPlasticTypes("Latitude 64");

      expect(result.success).toBe(true);
      expect(result.data).toContain("Opto");
      expect(result.data).toContain("Gold");
      expect(result.data).toContain("Zero Soft");
    });
  });

  describe("MVP Disc Sports Collection", () => {
    const mockMVPData = [
      {
        manufacturer: "MVP Disc Sports",
        mold: "Neutron",
        speed: 3,
        glide: 3,
        turn: 0,
        fade: 2,
        category: "Putter",
        plasticTypes: ["Neutron", "Electron"],
        weightRange: { min: 170, max: 176 },
        stability: "Stable",
        discontinued: false,
        releaseYear: 2012,
      },
      {
        manufacturer: "MVP Disc Sports",
        mold: "Vector",
        speed: 5,
        glide: 3,
        turn: 0,
        fade: 2,
        category: "Midrange",
        plasticTypes: ["Neutron", "Proton"],
        weightRange: { min: 170, max: 180 },
        stability: "Stable",
        discontinued: false,
        releaseYear: 2014,
      },
    ];

    it("should search MVP discs successfully", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockMVPData),
      });

      const result = await getDiscsByManufacturer("MVP Disc Sports");

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data![0].manufacturer).toBe("MVP Disc Sports");
      expect(result.data![0].mold).toBe("Neutron");
    });

    it("should filter MVP discs by category", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockMVPData),
      });

      const result = await searchManufacturerDatabase({
        manufacturer: "MVP",
        category: "Putter",
      });

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].mold).toBe("Neutron");
    });
  });

  describe("Axiom Discs Collection", () => {
    const mockAxiomData = [
      {
        manufacturer: "Axiom Discs",
        mold: "Envy",
        speed: 3,
        glide: 3,
        turn: 0,
        fade: 2,
        category: "Putter",
        plasticTypes: ["Neutron", "Electron", "Proton"],
        weightRange: { min: 170, max: 176 },
        stability: "Stable",
        discontinued: false,
        releaseYear: 2014,
      },
      {
        manufacturer: "Axiom Discs",
        mold: "Crave",
        speed: 6.5,
        glide: 5,
        turn: -1,
        fade: 1,
        category: "Fairway Driver",
        plasticTypes: ["Neutron", "Proton", "Plasma"],
        weightRange: { min: 165, max: 175 },
        stability: "Understable",
        discontinued: false,
        releaseYear: 2015,
      },
    ];

    it("should search Axiom discs successfully", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockAxiomData),
      });

      const result = await searchManufacturerDatabase({
        manufacturer: "Axiom",
        sortBy: "releaseYear",
        sortOrder: "asc",
      });

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data![0].mold).toBe("Envy"); // Released 2014
      expect(result.data![1].mold).toBe("Crave"); // Released 2015
    });

    it("should filter Axiom discs by stability", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockAxiomData),
      });

      const result = await searchManufacturerDatabase({
        manufacturer: "Axiom",
        stability: "Understable",
      });

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].mold).toBe("Crave");
    });
  });

  describe("Streamline Discs Collection", () => {
    const mockStreamlineData = [
      {
        manufacturer: "Streamline Discs",
        mold: "Pilot",
        speed: 2,
        glide: 5,
        turn: -1,
        fade: 1,
        category: "Putter",
        plasticTypes: ["Neutron", "Electron"],
        weightRange: { min: 170, max: 176 },
        stability: "Understable",
        discontinued: false,
        releaseYear: 2016,
      },
      {
        manufacturer: "Streamline Discs",
        mold: "Trace",
        speed: 11,
        glide: 5,
        turn: -1,
        fade: 2,
        category: "Distance Driver",
        plasticTypes: ["Neutron", "Proton"],
        weightRange: { min: 165, max: 175 },
        stability: "Stable",
        discontinued: false,
        releaseYear: 2017,
      },
    ];

    it("should search Streamline discs successfully", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockStreamlineData),
      });

      const result = await searchManufacturerDatabase({
        manufacturer: "Streamline",
        releaseYearRange: { min: 2016, max: 2018 },
      });

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data![0].manufacturer).toBe("Streamline Discs");
    });

    it("should filter Streamline discs by speed", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockStreamlineData),
      });

      const result = await searchManufacturerDatabase({
        manufacturer: "Streamline",
        speed: 11,
      });

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].mold).toBe("Trace");
    });
  });

  describe("Multi-Manufacturer Statistics", () => {
    const mockAllManufacturerData = [
      // Latitude 64
      {
        manufacturer: "Latitude 64",
        mold: "River",
        category: "Fairway Driver",
        speed: 7,
        discontinued: false,
        releaseYear: 2009,
        plasticTypes: ["Opto", "Gold"],
      },
      // MVP
      {
        manufacturer: "MVP Disc Sports",
        mold: "Neutron",
        category: "Putter",
        speed: 3,
        discontinued: false,
        releaseYear: 2012,
        plasticTypes: ["Neutron", "Electron"],
      },
      // Axiom
      {
        manufacturer: "Axiom Discs",
        mold: "Envy",
        category: "Putter",
        speed: 3,
        discontinued: false,
        releaseYear: 2014,
        plasticTypes: ["Neutron", "Proton"],
      },
      // Streamline
      {
        manufacturer: "Streamline Discs",
        mold: "Pilot",
        category: "Putter",
        speed: 2,
        discontinued: false,
        releaseYear: 2016,
        plasticTypes: ["Neutron"],
      },
    ];

    it("should provide comprehensive statistics for all manufacturers", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockAllManufacturerData),
      });

      const stats = await getManufacturerStatistics();

      expect(stats.success).toBe(true);
      expect(stats.data!.totalDiscs).toBe(4);
      expect(stats.data!.manufacturerCount).toBe(4);
      expect(stats.data!.discontinuedCount).toBe(0);
      expect(stats.data!.averageSpeed).toBe(3.8); // (7+3+3+2)/4 = 3.75, rounded to 3.8
      expect(stats.data!.oldestDisc!.mold).toBe("River");
      expect(stats.data!.newestDisc!.mold).toBe("Pilot");
      
      // Check manufacturer breakdown
      expect(stats.data!.manufacturerBreakdown["Latitude 64"]).toBe(1);
      expect(stats.data!.manufacturerBreakdown["MVP Disc Sports"]).toBe(1);
      expect(stats.data!.manufacturerBreakdown["Axiom Discs"]).toBe(1);
      expect(stats.data!.manufacturerBreakdown["Streamline Discs"]).toBe(1);
      
      // Check category breakdown
      expect(stats.data!.categoryBreakdown["Putter"]).toBe(3);
      expect(stats.data!.categoryBreakdown["Fairway Driver"]).toBe(1);
    });

    it("should handle plastic type diversity across manufacturers", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockAllManufacturerData),
      });

      const plasticResult = await getManufacturerPlasticTypes();

      expect(plasticResult.success).toBe(true);
      expect(plasticResult.data).toContain("Opto"); // Latitude 64
      expect(plasticResult.data).toContain("Neutron"); // MVP/Axiom/Streamline
      expect(plasticResult.data).toContain("Electron"); // MVP
      expect(plasticResult.data).toContain("Proton"); // Axiom
      expect(plasticResult.data).toContain("Gold"); // Latitude 64
    });
  });

  describe("Data Validation", () => {
    it("should validate manufacturer data structure consistency", async () => {
      const manufacturers = ["Latitude 64", "MVP Disc Sports", "Axiom Discs", "Streamline Discs"];
      
      for (const manufacturer of manufacturers) {
        const mockData = [
          {
            manufacturer,
            mold: "TestDisc",
            speed: 5,
            glide: 4,
            turn: 0,
            fade: 2,
            category: "Midrange",
            plasticTypes: ["TestPlastic"],
            weightRange: { min: 170, max: 180 },
            stability: "Stable",
            discontinued: false,
            releaseYear: 2020,
          },
        ];

        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockData),
        });

        const result = await getDiscsByManufacturer(manufacturer);

        expect(result.success).toBe(true);
        expect(result.data).toHaveLength(1);
        
        const disc = result.data![0];
        expect(disc).toHaveProperty("id");
        expect(disc).toHaveProperty("manufacturer");
        expect(disc).toHaveProperty("mold");
        expect(disc).toHaveProperty("flightNumbers");
        expect(disc.flightNumbers).toHaveProperty("speed");
        expect(disc.flightNumbers).toHaveProperty("glide");
        expect(disc.flightNumbers).toHaveProperty("turn");
        expect(disc.flightNumbers).toHaveProperty("fade");
      }
    });
  });
});
