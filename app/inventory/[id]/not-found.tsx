/**
 * Not Found Component for Disc Detail Page
 *
 * This component is displayed when a disc with the specified ID
 * cannot be found in the collection. It provides helpful navigation
 * options to get the user back to useful content.
 */

import { Layout, PageContainer } from "@/components/layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Search, ArrowLeft, Plus, Home } from "lucide-react";
import Link from "next/link";

/**
 * Not found page for disc detail routes
 *
 * Displayed when:
 * - Disc ID doesn't exist in the collection
 * - Invalid disc ID format
 * - Disc was deleted but URL was bookmarked
 *
 * @returns JSX element with not found UI
 */
export default function DiscNotFound() {
  return (
    <Layout>
      <PageContainer
        title="Disc Not Found"
        description="The disc you're looking for doesn't exist"
        actions={
          <Button asChild variant="outline">
            <Link href="/inventory">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Inventory
            </Link>
          </Button>
        }
      >
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                <Search className="h-6 w-6 text-blue-600" />
              </div>
              <CardTitle className="text-xl">Disc Not Found</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-muted-foreground">
                The disc you're looking for doesn't exist in your collection. 
                It may have been deleted or the link might be incorrect.
              </p>

              {/* Helpful suggestions */}
              <div className="text-left space-y-2 p-4 bg-muted rounded-lg">
                <h4 className="font-medium text-sm">What you can do:</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Check if the disc ID in the URL is correct</li>
                  <li>• Browse your inventory to find the disc</li>
                  <li>• Add a new disc if it's missing from your collection</li>
                </ul>
              </div>

              {/* Action buttons */}
              <div className="flex flex-col gap-2 pt-4">
                <Button asChild className="w-full">
                  <Link href="/inventory">
                    <Search className="h-4 w-4 mr-2" />
                    Browse Inventory
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link href="/inventory/add">
                    <Plus className="h-4 w-4 mr-2" />
                    Add New Disc
                  </Link>
                </Button>
                <Button asChild variant="ghost" className="w-full">
                  <Link href="/">
                    <Home className="h-4 w-4 mr-2" />
                    Go Home
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageContainer>
    </Layout>
  );
}
