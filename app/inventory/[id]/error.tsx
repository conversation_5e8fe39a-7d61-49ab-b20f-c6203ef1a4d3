/**
 * Error Component for Disc Detail Page
 *
 * This component handles errors that occur while loading or rendering
 * the disc detail page. It provides user-friendly error messages and
 * recovery options.
 */

"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Layout, PageContainer } from "@/components/layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, ArrowLeft, RefreshCw, Home } from "lucide-react";

// ============================================================================
// TYPES
// ============================================================================

interface DiscDetailErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Error boundary component for disc detail page
 *
 * Provides user-friendly error handling with recovery options:
 * - Retry the failed operation
 * - Navigate back to inventory
 * - Go to home page
 *
 * @param props - Error props from Next.js error boundary
 * @returns JSX element with error UI
 */
export default function DiscDetailError({ error, reset }: DiscDetailErrorProps) {
  const router = useRouter();

  useEffect(() => {
    // Log error to console for debugging
    console.error("Disc detail page error:", error);
  }, [error]);

  const handleRetry = () => {
    reset();
  };

  const handleBackToInventory = () => {
    router.push("/inventory");
  };

  const handleGoHome = () => {
    router.push("/");
  };

  // Determine error type and message
  const isNotFound = error.message.includes("not found") || error.message.includes("404");
  const isNetworkError = error.message.includes("fetch") || error.message.includes("network");

  const errorTitle = isNotFound 
    ? "Disc Not Found" 
    : isNetworkError 
    ? "Connection Error" 
    : "Something Went Wrong";

  const errorMessage = isNotFound
    ? "The disc you're looking for doesn't exist or may have been deleted."
    : isNetworkError
    ? "Unable to load disc details. Please check your connection and try again."
    : "An unexpected error occurred while loading the disc details.";

  return (
    <Layout>
      <PageContainer
        title="Error"
        description="Unable to load disc details"
        actions={
          <Button variant="outline" onClick={handleBackToInventory}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Inventory
          </Button>
        }
      >
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <CardTitle className="text-xl">{errorTitle}</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-muted-foreground">{errorMessage}</p>
              
              {/* Error details for debugging (only in development) */}
              {process.env.NODE_ENV === "development" && (
                <details className="text-left">
                  <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
                    Technical Details
                  </summary>
                  <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto">
                    {error.message}
                    {error.digest && `\nDigest: ${error.digest}`}
                  </pre>
                </details>
              )}

              {/* Action buttons */}
              <div className="flex flex-col sm:flex-row gap-2 pt-4">
                <Button onClick={handleRetry} className="flex-1">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <Button variant="outline" onClick={handleBackToInventory} className="flex-1">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Inventory
                </Button>
                <Button variant="ghost" onClick={handleGoHome} className="flex-1">
                  <Home className="h-4 w-4 mr-2" />
                  Home
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageContainer>
    </Layout>
  );
}
