/**
 * Loading Component for Disc Detail Page
 *
 * This component provides a loading state while the disc detail page
 * is being rendered. It follows the existing loading patterns used
 * throughout the application.
 */

import { Layout, PageContainer, Section } from "@/components/layout";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

/**
 * Loading state for disc detail page
 *
 * Provides skeleton loading that matches the expected layout of the
 * disc detail page to minimize layout shift when content loads.
 *
 * @returns JSX element with loading skeletons
 */
export default function DiscDetailLoading() {
  return (
    <Layout>
      <PageContainer
        title={<Skeleton className="h-8 w-48" />}
        description={<Skeleton className="h-4 w-64" />}
        actions={
          <div className="flex items-center gap-2">
            <Skeleton className="h-9 w-20" />
            <Skeleton className="h-9 w-16" />
          </div>
        }
      >
        <div className="space-y-6">
          {/* Main disc information card */}
          <Section>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Disc image */}
                  <div className="space-y-4">
                    <Skeleton className="aspect-square w-full rounded-lg" />
                    <div className="flex gap-2">
                      <Skeleton className="h-8 w-16" />
                      <Skeleton className="h-8 w-16" />
                      <Skeleton className="h-8 w-16" />
                      <Skeleton className="h-8 w-16" />
                    </div>
                  </div>

                  {/* Disc details */}
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Skeleton className="h-4 w-20 mb-2" />
                        <Skeleton className="h-6 w-24" />
                      </div>
                      <div>
                        <Skeleton className="h-4 w-16 mb-2" />
                        <Skeleton className="h-6 w-20" />
                      </div>
                      <div>
                        <Skeleton className="h-4 w-24 mb-2" />
                        <Skeleton className="h-6 w-28" />
                      </div>
                      <div>
                        <Skeleton className="h-4 w-18 mb-2" />
                        <Skeleton className="h-6 w-16" />
                      </div>
                    </div>
                    <div>
                      <Skeleton className="h-4 w-16 mb-2" />
                      <Skeleton className="h-20 w-full" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Section>

          {/* Usage history section */}
          <Section>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center gap-3">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-48 mb-1" />
                        <Skeleton className="h-3 w-32" />
                      </div>
                      <Skeleton className="h-3 w-16" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </Section>

          {/* Related discs section */}
          <Section>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-40" />
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(3)].map((_, i) => (
                    <Card key={i}>
                      <CardContent className="p-4">
                        <Skeleton className="aspect-square w-full rounded-lg mb-3" />
                        <Skeleton className="h-4 w-24 mb-2" />
                        <Skeleton className="h-3 w-20" />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </Section>
        </div>
      </PageContainer>
    </Layout>
  );
}
