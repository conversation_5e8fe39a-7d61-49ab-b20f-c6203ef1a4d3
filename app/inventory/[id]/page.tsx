/**
 * Disc Detail Page for Disc Golf Inventory Management System
 *
 * This page displays detailed information about a specific disc, including:
 * - Complete disc specifications and metadata
 * - Edit capabilities with inline editing
 * - Usage history tracking
 * - Related disc suggestions
 * - Comparison tools
 *
 * Requirements Satisfied:
 * - FR-DETAIL-001: Detailed view with all disc information, edit capabilities, and usage history
 * - FR-DETAIL-002: Related disc suggestions and comparison tools
 * - FR-DETAIL-003: Inline editing with real-time validation
 */

import { Metadata } from "next";
import { notFound } from "next/navigation";
import { DiscDetailPage } from "@/components/inventory/DiscDetailPage";

// ============================================================================
// TYPES
// ============================================================================

interface DiscDetailPageProps {
  params: {
    id: string;
  };
}

// ============================================================================
// METADATA
// ============================================================================

export async function generateMetadata({ params }: DiscDetailPageProps): Promise<Metadata> {
  // Note: In a real app with server-side data, we'd fetch the disc here
  // For now, we'll use a generic title and update it client-side
  return {
    title: `Disc Details | Disc Golf Inventory`,
    description: `View and edit details for disc ${params.id}`,
    robots: {
      index: false, // Don't index individual disc pages
      follow: true,
    },
  };
}

// ============================================================================
// PAGE COMPONENT
// ============================================================================

/**
 * Disc Detail Page Component
 *
 * This page component handles the routing and initial setup for displaying
 * disc details. The actual implementation is delegated to the DiscDetailPage
 * component for better separation of concerns and testability.
 *
 * @param props - Page props containing the disc ID
 * @returns JSX element or notFound() if disc doesn't exist
 */
export default function DiscDetailPageRoute({ params }: DiscDetailPageProps) {
  const { id } = params;

  // Validate that we have a disc ID
  if (!id || typeof id !== "string") {
    notFound();
  }

  // Render the detail page component
  return <DiscDetailPage discId={id} />;
}
